import React, { useState, useContext } from 'react';
import axios from 'axios';
import { AppContext } from '../context/AppContext';

const DebugTours = () => {
  const { backendUrl } = useContext(AppContext);
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);

  const testAPI = async () => {
    setLoading(true);
    try {
      console.log('Testing API call to:', `${backendUrl}/api/tours`);
      const response = await axios.get(`${backendUrl}/api/tours`);
      console.log('API Response:', response.data);
      setResult(JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.error('API Error:', error);
      setResult(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 bg-white rounded shadow">
      <h3 className="text-lg font-bold mb-4">Debug Tours API</h3>
      <p className="mb-2">Backend URL: {backendUrl}</p>
      <button 
        onClick={testAPI}
        disabled={loading}
        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
      >
        {loading ? 'Testing...' : 'Test API Call'}
      </button>
      {result && (
        <pre className="mt-4 p-4 bg-gray-100 rounded text-sm overflow-auto max-h-96">
          {result}
        </pre>
      )}
    </div>
  );
};

export default DebugTours;
