import React from "react";
import Navbar from "./components/Navbar";
import Footer from "./components/Footer";
import { Route, Routes } from "react-router-dom";
import Tour from "./pages/Tour";
import TourDetails from "./pages/TourDetails";
import Login from "./pages/Login";
import Home from "./pages/Home";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Booking from "./pages/Booking";
import Invoice from "./pages/Invoice";
import About from "./pages/About";
import MyBookings from "./pages/MyBookings";
import AdminDashboard from "./pages/admin/AdminDashboard";
import ToursManagement from "./pages/admin/ToursManagement";
import TourForm from "./pages/admin/TourForm";

const App = () => {
  return (
    <div className="flex flex-col min-h-screen">
      <ToastContainer theme="dark" position="bottom-right" autoClose={1000} />

      <Routes>
        {/* Admin Routes - No navbar/footer */}
        <Route path="/admin" element={<AdminDashboard />} />
        <Route path="/admin/tours" element={<ToursManagement />} />
        <Route path="/admin/tours/add" element={<TourForm />} />
        <Route path="/admin/tours/edit/:id" element={<TourForm />} />

        {/* Public Routes - With navbar/footer */}
        <Route path="/*" element={
          <div className="flex flex-col min-h-screen px-4 sm:px-8 md:px-10 lg:px-22 bg-gradient-to-b from-sky-100 to-indigo-100">
            <Navbar />
            <main className="flex-1">
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/tours" element={<Tour />} />
                <Route path="/about" element={<About />} />
                <Route path="/tours/:id" element={<TourDetails />} />
                <Route path="/login" element={<Login />} />
                <Route path="/booking" element={<Booking />} />
                <Route path="/my-bookings" element={<MyBookings />} />
                <Route path="/invoice" element={<Invoice />} />
              </Routes>
            </main>
            <Footer />
          </div>
        } />
      </Routes>
    </div>
  );
};

export default App;
