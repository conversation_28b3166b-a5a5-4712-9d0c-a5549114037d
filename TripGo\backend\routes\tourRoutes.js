import express from "express";
import {
  getAllTours,
  getTourById,
  createTour,
  updateTour,
  deleteTour,
  getAdminTours,
} from "../controllers/tourController.js";
import { adminAuth, verifyToken } from "../middleware/auth.js";

const tourRouter = express.Router();

// Public routes
tourRouter.get("/", getAllTours);
tourRouter.get("/:id", getTourById);

// Admin routes
tourRouter.get("/admin/all", adminAuth, getAdminTours);
tourRouter.post("/admin", adminAuth, createTour);
tourRouter.put("/admin/:id", adminAuth, updateTour);
tourRouter.delete("/admin/:id", adminAuth, deleteTour);

export default tourRouter;
